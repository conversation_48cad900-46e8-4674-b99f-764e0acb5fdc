import React, { Component } from 'react';
import {
  Modal,
  Form,
  Select,
  Button,
  message,
  Descriptions,
  Card,
  Row,
  Col,
  Tag,
  InputNumber,
  Divider,
} from 'antd';
import {
  CONTRACT_SIGNING_STATUS,
  getContractTypeLabel,
  // getContractSigningStatusConfig,
  formatCurrency,
  formatPercentage,
} from '../../utils/projectUtils';
import { projectAPI, departmentAPI } from '../../services/api';

const { Option } = Select;

// 表单校验规则
const formRules = {
  executivePM: [{ required: true, message: '请选择执行PM' }],
  contentMedia: [
    { required: true, message: '请至少选择一个内容媒介' },
    { type: 'array', min: 1, message: '请至少选择一个内容媒介' },
  ],
  contractSigningStatus: [{ required: true, message: '请选择合同签署状态' }],
  cost: [
    { required: true, message: '请输入成本信息' },
  ],
};

class ProjectEditModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      pmOptions: [], // 执行PM选项
      mediaOptions: [], // 内容媒介选项
    };
  }

  componentDidMount() {
    this.loadOptions();
    // 如果有项目数据，设置表单值
    if (this.props.project) {
      setTimeout(() => {
        const formData = this.transformProjectData(this.props.project);
        this.props.form.setFieldsValue(formData);
      }, 100);
    }
  }

  componentDidUpdate(prevProps) {
    // 当项目数据变化时，更新表单值
    if (this.props.project && this.props.project !== prevProps.project) {
      const formData = this.transformProjectData(this.props.project);
      this.props.form.setFieldsValue(formData);
    }
  }

  // 加载选项数据
  loadOptions = async () => {
    try {
      // 加载执行PM选项（从项目管理部门获取）
      let pmOptions = [];
      const pmResponse = await departmentAPI.getDepartmentUsers({
        // deptId: 1,
        size: 100,
      });
      console.log('[ getDepartmentUsers ] >', pmResponse.data);
      if (pmResponse.success) {
        pmOptions = pmResponse.data.list.map((user) => ({
          value: user.userid,
          label: user.name,
          avatar: user.avatar,
          mobile: user.mobile,
          email: user.email,
        }));
      }

      // 加载内容媒介选项（从内容部门获取）
      let mediaOptions = [];
      const mediaResponse = await departmentAPI.getDepartmentUsers({
        deptId: '',
        size: 100,
      });
      if (mediaResponse.success) {
        mediaOptions = mediaResponse.data.list.map((user) => ({
          value: user.userid,
          label: user.name,
          avatar: user.avatar,
          mobile: user.mobile,
          email: user.email,
        }));
      }
      this.setState({
        pmOptions,
        mediaOptions,
      });
    } catch (error) {
      console.error('Load options failed:', error);
      message.error('加载选项数据失败');
    }
  };

  // 转换项目数据为表单格式
  transformProjectData = (project) => {
    console.log('[ project ] >', project);
    let contentMedia = [];
    if (Array.isArray(project.contentMedia)) {
      contentMedia = project.contentMedia;
    } else if (project.contentMedia) {
      contentMedia = project.contentMedia.split(', ');
    }
    return {
      brandName: project.brand.name,
      projectName: project.projectName,
      executionPeriod: project.executionPeriod,
      planningBudget: project.planningBudget,
      talentBudget: project.talentBudget,
      adBudget: project.adBudget,
      otherBudget: project.otherBudget,
      estimatedTalentRebate: project.estimatedTalentRebate,
      talentCost: project.talentCost,
      adCost: project.adCost,
      otherCost: project.otherCost,
      projectProfit: project.projectProfit,
      grossMargin: project.grossMargin,
      executivePM: project.executivePM?.userid,
      contentMedia,
      contractSigningStatus: project.contractSigningStatus.toUpperCase(),
    };
  };

  // 处理表单提交
  handleSubmit = (e) => {
    e.preventDefault();
    this.props.form.validateFields(async (err, values) => {
      if (err) {
        message.error('请检查表单填写是否正确');
        return;
      }

      this.setState({ loading: true });

      try {
        // 构建更新数据，只包含可编辑的字段
        const updateData = {
          executorPM: values.executivePM,
          contentMediaIds: values.contentMedia,
          contractSigningStatus: values.contractSigningStatus.toLowerCase(),
          cost: {
            influencerCost: values.talentCost,
            adCost: values.adCost,
            otherCost: values.otherCost,
            estimatedInfluencerRebate: values.estimatedTalentRebate,
          },
          id: this.props.project.id,
        };

        console.log('[ updateData ] >', updateData);
        const response = await projectAPI.updateProject(updateData);

        if (response.success) {
          message.success('项目更新成功');
          if (this.props.onSubmit) {
            this.props.onSubmit(response.data);
          }
        } else {
          message.error(response.message || '更新失败');
        }
      } catch (error) {
        console.error('Update project failed:', error);
        message.error('更新失败');
      } finally {
        this.setState({ loading: false });
      }
    });
  };

  // 格式化日期范围显示
  formatExecutionPeriod = (period) => {
    if (Array.isArray(period) && period.length === 2) {
      return `${period[0]} ~ ${period[1]}`;
    }
    return '-';
  };

  render() {
    const { visible, onCancel, project } = this.props;
    const { loading, pmOptions, mediaOptions } = this.state;
    const { getFieldDecorator } = this.props.form;

    if (!project) {
      return null;
    }

    return (
      <Modal
        title={`编辑项目 - ${project.projectName}`}
        visible={visible}
        onCancel={onCancel}
        footer={null}
        width={900}
        destroyOnClose
      >
        <div style={{ maxHeight: '70vh', overflowY: 'auto' }}>
          {/* 项目基本信息展示 */}
          <Card title="项目信息" size="small" style={{ marginBottom: 16 }}>
            <Descriptions column={2} size="small">
              <Descriptions.Item label="项目名称" span={2}>
                {project.projectName}
              </Descriptions.Item>
              <Descriptions.Item label="所属品牌">
                {project.brandName}
              </Descriptions.Item>
              <Descriptions.Item label="合同类型">
                <Tag color="blue">
                  {getContractTypeLabel(project.contractType)}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="执行周期" span={2}>
                {this.formatExecutionPeriod(project.executionPeriod)}
              </Descriptions.Item>
              {/* <Descriptions.Item label="项目状态">
                <Tag color={getProjectStatusConfig(project.status).color}>
                  {getProjectStatusConfig(project.status).label}
                </Tag>
              </Descriptions.Item> */}
              <Descriptions.Item label="创建时间">
                {project.createTime}
              </Descriptions.Item>
              <Descriptions.Item label="更新时间">
                {project.updateTime}
              </Descriptions.Item>
              <Descriptions.Item label="备注">
                {project.remark}
              </Descriptions.Item>

            </Descriptions>
          </Card>

          {/* 财务信息展示 */}
          <Card title="财务信息" size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={9}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '12px', color: '#666' }}>规划预算</div>
                  <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#1890ff' }}>
                    {formatCurrency(project.planningBudget)}
                    <div style={{ fontSize: '12px', color: '#8e8e8e' }}>(达人: {formatCurrency(project.talentBudget)}, 投流: {formatCurrency(project.adBudget)}, 其他: {formatCurrency(project.otherBudget)})</div>
                  </div>
                </div>
              </Col>
              <Col span={5}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '12px', color: '#666' }}>达人预计收入</div>
                  <div style={{
                    fontSize: '16px',
                    fontWeight: 'bold',
                    color: (project.estimatedTalentRebate || 0) >= 0 ? '#1890ff' : '#f5222d',
                  }}
                  >
                    {formatCurrency(project.estimatedTalentRebate)}
                  </div>
                </div>
              </Col>
              <Col span={5}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '12px', color: '#666' }}>项目利润</div>
                  <div style={{
                    fontSize: '16px',
                    fontWeight: 'bold',
                    color: (project.projectProfit || 0) >= 0 ? '#52c41a' : '#f5222d',
                  }}
                  >
                    {formatCurrency(project.projectProfit)}
                  </div>
                </div>
              </Col>
              <Col span={5}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '12px', color: '#666' }}>毛利率</div>
                  <div style={{
                    fontSize: '16px',
                    fontWeight: 'bold',
                    color: (project.grossMargin || 0) >= 0 ? '#52c41a' : '#f5222d',
                  }}
                  >
                    {formatPercentage(project.grossMargin)}
                  </div>
                </div>
              </Col>
            </Row>
          </Card>

          <Divider>可编辑字段</Divider>

          {/* 可编辑字段表单 */}
          <Form layout="vertical" onSubmit={this.handleSubmit}>
            <Card title="编辑信息" size="small">

              <Row gutter={16}>
                <Col span={6}>
                  <Form.Item label="达人成本">
                    {getFieldDecorator('talentCost', {
                      rules: formRules.talentCost,
                    })(
                      <InputNumber
                        placeholder="请输入达人成本"
                        style={{ width: '100%' }}
                        formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                        parser={(value) => value.replace(/¥\s?|(,*)/g, '')}
                      />,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="投流成本">
                    {getFieldDecorator('adCost', {
                      rules: formRules.adCost,
                    })(
                      <InputNumber
                        placeholder="请输入投流成本"
                        style={{ width: '100%' }}
                        formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                        parser={(value) => value.replace(/¥\s?|(,*)/g, '')}
                      />,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="其他成本">
                    {getFieldDecorator('otherCost', {
                      rules: formRules.otherCost,
                    })(
                      <InputNumber
                        placeholder="请输入其他成本"
                        style={{ width: '100%' }}
                        formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                        parser={(value) => value.replace(/¥\s?|(,*)/g, '')}
                      />,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6} style={{ display: 'none' }}>
                  <Form.Item label="预估达人返点">
                    {getFieldDecorator('estimatedTalentRebate', {
                      rules: formRules.estimatedTalentRebate,
                    })(
                      <InputNumber
                        placeholder="请输入预估达人返点"
                        style={{ width: '100%' }}
                        formatter={(value) => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                        parser={(value) => value.replace(/¥\s?|(,*)/g, '')}
                      />,
                    )}
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={6}>
                  <Form.Item label="执行PM">
                    {getFieldDecorator('executivePM', {
                      rules: formRules.executivePM,
                    })(
                      <Select placeholder="请选择执行PM" showSearch optionFilterProp="children">
                        {pmOptions.map((user) => (
                          <Option
                            key={user.value}
                            value={user.value}
                            title={user.email || user.mobile}
                          >
                            {user.label}
                          </Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="内容媒介">
                    {getFieldDecorator('contentMedia', {
                      rules: formRules.contentMedia,
                    })(
                      <Select
                        mode="multiple"
                        placeholder="请选择内容媒介"
                        showSearch
                        optionFilterProp="children"
                      >
                        {mediaOptions.map((user) => (
                          <Option
                            key={user.value}
                            value={user.value}
                            title={user.email || user.mobile}
                          >
                            {user.label}
                          </Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="合同签署状态">
                    {getFieldDecorator('contractSigningStatus', {
                      rules: formRules.contractSigningStatus,
                    })(
                      <Select placeholder="请选择合同签署状态">
                        {CONTRACT_SIGNING_STATUS.map((status) => (
                          <Option key={status.value} value={status.value}>
                            <Tag color={status.color} style={{ marginRight: 8 }}>
                              {status.label}
                            </Tag>
                            {status.label}
                          </Option>
                        ))}
                      </Select>,
                    )}
                  </Form.Item>
                </Col>
              </Row>

              <Row style={{ marginTop: 16, textAlign: 'center' }}>
                <Col span={24}>
                  <Button onClick={onCancel} style={{ marginRight: 16 }}>
                    取消
                  </Button>
                  <Button type="primary" loading={loading} htmlType="submit">
                    保存修改
                  </Button>
                </Col>
              </Row>
            </Card>
          </Form>
        </div>
      </Modal>
    );
  }
}

export default Form.create()(ProjectEditModal);
